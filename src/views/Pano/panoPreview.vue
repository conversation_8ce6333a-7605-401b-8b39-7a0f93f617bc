<template>
  <div class="add-container">
    <van-sticky>
      <van-nav-bar
          title="全景图预览"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />
    </van-sticky>
    <div class="preview-container">
      <iframe class="preview" :src="iframeUrl" frameborder="0"></iframe>
    </div>
  </div>
</template>

<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, ref} from "vue";
import {getToken} from "@/utils/token";
import {useRoute} from "vue-router";
const route = useRoute()
const iframeUrl = ref('')
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
onMounted(()=>{
  const PbGuid = route.query.PbGuid
  const ProjectId = route.query.ProjectId
  const SceneName = route.query.SceneName
  if (PbGuid && ProjectId) {
    iframeUrl.value = SceneName ? `${window.IP_CONFIG.FRONT_URL}/#/LinkShare/PanoShare/${PbGuid}/${ProjectId}/${SceneName}` : `${window.IP_CONFIG.FRONT_URL}/#/LinkShare/PanoShare/${PbGuid}/${ProjectId}`
  }
})

</script>
<style scoped lang="scss">
.add-container{
  height: 100%;
  background-color: #FFF;
  .preview-container{
    height: calc(100% - 46px);
    .preview{
      width: 100%;
      height: 100%;
    }
  }
}

</style>
