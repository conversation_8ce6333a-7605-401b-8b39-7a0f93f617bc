<template>
  <div class="document-container">
    <van-sticky>
      <van-nav-bar
          :title="getProjectName()"
          left-text="返回"
          left-arrow
          @click-left="clickLeft"
      />
    </van-sticky>
    <form action="/">
      <van-search
          class="van-search"
          v-model="searchValue"
          placeholder="请输入搜索关键词"
          @search="searchDocument"
      >
        <!-- <template #action>
          <div style="display: flex">
            <div @click="searchDocument">搜索</div>
            <div style="margin-left: 10px" @click="cancelSearch">取消</div>
          </div>
        </template> -->
      </van-search>
    </form>
    <div class="list-container">
        <div class="item-wrapper" v-for="item in documentData" :key="item" @click="clickItem(item)">
          <van-swipe-cell>
            <div class="top">
              <van-image
                  class="document-icon"
                  width="24px"
                  height="24px"
                  :src="handleIconSrc(item.FileExtension)"
              />
              <div class="center">
                <span class="top">{{ item.FileName }}</span>
                <div class="bottom">
                  <span class="left">{{ item.CreateTime }}</span>
                  <span class="right" v-if="!item.IsFolder">{{ item.FileSize + item.SizeUnit}}</span>
                </div>
              </div>
            </div>
            <template #right>
              <van-button type="danger" text="删除" :style="{height: '100%'}" @click="deleteDocument(item)"/>
            </template>
          </van-swipe-cell>
          <div class="line"></div>
        </div>
    </div>
    <Preview :url="previewUrl" @close-dialog="closeDialog" :visible="isPreview"></Preview>
  </div>
</template>

<script setup>
import {onMounted, ref} from "vue";
import {getOrganizeId} from "@/utils/organizeId";
import {getToken} from "@/utils/token";
import api from "@/api";
import {getUserInfo} from "@/utils/user";
import Preview from "@/views/Project/Preview.vue";
import {showToast} from "vant";
import router from "@/router";
import {getProjectName} from "@/utils/projectName";

const searchValue = ref('')

function handleIconSrc(extension) {
  if (extension.toLowerCase()){
    switch (extension) {
      case '.ppt':
      case '.pptx':
        return require('../../assets/images/document/ppt-icon.png')
      case '.doc':
      case '.docx':
        return require('../../assets/images/document/word-icon.png')
      case '.dwg':
        return require('../../assets/images/document/dwg-icon.png')
      case '.xls':
      case '.xlsx':
        return require('../../assets/images/document/excel-icon.png')
      case '.png':
      case '.jpeg':
      case '.jpg':
      case '.gif':
      case '.webp':
        return require('../../assets/images/document/image-icon.png')
      case '.txt':
        return require('../../assets/images/document/txt-icon.png')
      case '.pdf':
        return require('../../assets/images/document/pdf-icon.png')
      case '.mp4':
      case '.avi':
      case '.mkv':
      case '.mov':
        return require('../../assets/images/document/mp4-icon.png')
      case '.mp3':
      case '.acc':
      case '.flac':
        return require('../../assets/images/document/mp3-icon.png')
      case '.zip':
      case '.rar':
        return require('../../assets/images/document/zip-icon.png')
      case '.rvt':
        return require('../../assets/images/document/rvt-icon.png')
      default:
        return require('../../assets/images/document/unknown-icon.png')
    }
  }else {
    return require('../../assets/images/document/folder-icon.png')
  }

}
function clickLeft() {
  if (parentIds.value.length > 1 ){
    getDocumentList(parentIds.value[parentIds.value.length - 1 - 1])
    parentIds.value.splice(parentIds.value.length - 1 , 1)
  }else if (parentIds.value.length  === 1 ){
    getDocumentTree()
  }else {
    router.push('/home')
  }
}
const previewUrl = ref('')
const isPreview = ref(false)
const parentIds = ref([])
const commonTypes = ref(['.ppt','.pptx','.doc','.docx','.xls','.xlsx','.png','.jpeg','.jpg','.gif','.webp','.txt','.pdf','.mp4','.avi','.mkv','.mov','.mp3','.acc','.flac'])
const modelTypes =  ref(['.rvt','.mv','.dwg'])
/**
 * 下级操作
 */
function clickItem(item){
  if (item.IsFolder){
    // parentIds.value.push(item.FileId)
    getDocumentList(item.FileId, true)
  }else {
    console.log(commonTypes.value)
    if (commonTypes.value.includes(item.FileExtension)){
      const tempUrl = `${window.IP_CONFIG.BASE_URL}/api/v1/file/preview?FileId=${item.FileId}&Version=1&UserId=${JSON.parse(getUserInfo()).UserId}&Token=${getToken()}`
      previewUrl.value = `${window.IP_CONFIG.BASE_URL}/Content/PDFJS/web/viewer.html?file=${encodeURIComponent(tempUrl)}`;
      console.log('previewUrl.value', previewUrl.value)
      isPreview.value = true
    }else if (modelTypes.value.includes(item.FileExtension)){
      if (item.ModelId){
        previewUrl.value = `${window.IP_CONFIG.FRONT_URL}/scenemanager/#/?vaultID=${getOrganizeId()}&modelID=${item.ModelId}`
        isPreview.value = true
      }else {
        showToast('未转换的模型，无法打开！')
      }
    }else {
      showToast('暂不支持的格式，无法打开！')
    }
  }
}

/**
 * 删除文件
 */
async function deleteDocument(item) {
  const params = [{
    Id: item.FileId,
    IsFolder:item.IsFolder
  }]
  const res = await api.DeleteDocument(params)
  if (res.data.Ret === 1) {
    if (parentIds.value.length > 0) {
      getDocumentList(parentIds.value[parentIds.value.length - 1])
    }else {
      await getDocumentTree()
    }
  }
}

const rootFolderId = ref('')
/**
 * 获取文档树结构
 */
async function getDocumentTree() {
  const res = await api.GetDocumentTree({
    projectId: getOrganizeId(),
    Token: getToken(),
    parentId: 0,
  })
  if (res.data.Ret === 1) {
    parentIds.value = []
    if (res.data.Data.length > 0) {
      rootFolderId.value = res.data.Data[0].Id
      await getDocumentList(rootFolderId.value)
    } else {
      showToast('无查看权限！')
    }
  }
}

/**
 * 获取文档数据
 */
async function getDocumentList(folderId,isPush) {
  const res = await api.GetDocumentList({
    projectId: getOrganizeId(),
    folderId
  })
  if (res.data.Ret === 1) {
    // 根目录权限
    await getDocumentAuth(folderId,true,res.data.Data,isPush)
  }
}
/**
 * 获取文档数据
 */
async function searchDocument() {
  if (searchValue.value === '') {
    getDocumentTree()
    return
  }
  const res = await api.SearchDocument({
    projectId: getOrganizeId(),
    Token: getToken(),
    fileName: searchValue.value,
  })
  if (res.data.Ret === 1) {
   documentData.value = res.data.Data
  }else{
    showToast(res.data.Msg)
  }
}
const documentData = ref([]);
/**
 * 获取文档权限
 */
async function getDocumentAuth(objectId,isFolder,data,isPush) {
  // 第一级别文件直接显示
  if(objectId === rootFolderId.value) {
    documentData.value = data
    return
  }
  const res = await api.GetDocumentAuth({
    Token: getToken(),
    objectId,
    isFolder
  })
  if (res.data.Ret === 1){
    const open = haveOpenAuth(res.data.Data)
    if (open){
      documentData.value = data
      if(isPush) {
        parentIds.value.push(objectId)
      }
    } else {
      showToast('无打开权限!')
    }
  }
}
/**
 * 判断打开权限
 */
function haveOpenAuth(data){
  let open = false
  data.forEach(element => {
    if (element.Open){
      open = true
    }
  })
  return open
}
/**
 * 取消搜索
 */
function cancelSearch(){
  searchValue.value = ''
  getDocumentTree()
}
function closeDialog(){
  isPreview.value = false
}
onMounted(()=>{
  getDocumentTree()
})
</script>

<style scoped lang="scss">
.document-container {
  height: calc(100% - 150px);
  background-color: #fff;
  .list-container{
    background-color: #fff;
    height: 100%;
    overflow: auto;
    .item-wrapper{
        padding-left: 25px;
        width: 100%;
        background-color: #fff;
        height: 64px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        .top{
          height: 62px;
          display: flex;
          .document-icon{
            margin-top: 20px;
          }
          .center{
            width: calc(100% - 50px);
            margin-left: 15px;
            display: flex;
            flex-direction: column;
            .top{
              margin-top: 12px;
              display: inline-block;
              max-width: 300px;
              font-weight: 500;
              font-size: 16px;
              color: #283A4F;
              /* 超出部分隐藏 */
              overflow: hidden;
              /* 不换行 */
              white-space: nowrap;
              /* 溢出用省略号代替 */
              text-overflow: ellipsis;
            }
            .bottom{
              margin-bottom: 5px;
              display: flex;
              width: 100%;
              .left{
                max-width: 186px;
                font-weight: 500;
                font-size: 12px;
                color: #A6AEB6;
              }
              .right{
                margin-left: auto;
                margin-right: 12px;
                max-width: 186px;
                font-weight: 500;
                font-size: 12px;
                color: #A6AEB6;
              }
            }
          }
        }
        .line{
          width: 100%;
          height: 1px;
          background-color: #f2f2f2;
        }
      }
  }
}

</style>