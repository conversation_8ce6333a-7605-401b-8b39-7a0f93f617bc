<template>
<div class="flow-container">
  <van-sticky>
    <van-nav-bar
        v-if="isApp"
        title="业务流程"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
    />
    <van-dropdown-menu v-if="isBusiness">
      <van-dropdown-item v-model="organizeId" :options="projectOption"/>
    </van-dropdown-menu>
    <van-tabs v-model:active="active" @click-tab="onClickTab" sticky animated>
      <van-tab title="我的待办" name="Todolist_Init"></van-tab>
      <van-tab title="流转中" name="Runing_Init"></van-tab>
      <van-tab title="已完结" name="Complete_Init"></van-tab>
    </van-tabs>
    <van-search
        v-if="false"
        v-model="searchValue"
        shape="round"
        show-action
        placeholder="输入关键词"
        @search="getList"
        @cancel="cancelSearch"
    />
  </van-sticky>
  <div class="list-container">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
    <van-list
        v-model:loading="loading"
        :finished="finished"
        @load="onLoad"
    >
      <div class="item-wrapper" v-for="item in doneData" :key="item" @click="clickItem(item)">
        <van-image
            class="item-img"
            width="16px"
            height="16px"
            :src="require('../../assets/images/flow/flow-icon.png')"
        />
        <span> {{item.name}}</span>
        <span class="item-nums"> {{item.data.length}}</span>
      </div>
    </van-list>
    </van-pull-refresh>
  </div>

</div>
</template>

<script setup>
import {onMounted, ref, watch} from "vue";
import api from "@/api";
import router from "@/router";
import {getToken, setToken} from "@/utils/token";
import {getCCToken, setCCToken} from "@/utils/cctoken";
import {getOrganizeId, setOrganizeId} from "@/utils/organizeId";
import {showToast} from "vant";
import {setUserInfo} from "@/utils/user";
import {useRoute} from "vue-router";

const active = ref(0)
const flowType =ref('Todolist_Init')
const onClickTab = ({ title,name }) => {
  flowType.value = name
  getList()
};

const  searchValue = ref('')
function cancelSearch() {
  searchValue.value = ''
  getList()
}
let loading = ref(false);
const finished = ref(true);
const token = ref('')
let isBusiness = ref(false)
let isApp = ref(false)
onMounted(()=>{
  isBusiness = window.IP_CONFIG.IS_BUSINESS
  if (window.IP_CONFIG.IS_BUSINESS){
    let outerToken
    if (window.IP_CONFIG.IS_QUERY){
      const route = useRoute()
      outerToken = route.query.Token
    }else {
      // 获取外部token
      outerToken = location.search
      if (outerToken && outerToken.length > 7){
        outerToken = outerToken.slice(7)
      }else {
        outerToken = sessionStorage.getItem('token')
      }
    }
    // 获取BIMe-->Token
    getOuterToken(outerToken)
  }else {
    isApp = true
    const route = useRoute()
    const mToken = route.query.token
    if (!mToken) {
      token.value = getToken()
      console.log('路由地址Token为空')
    } else {
      token.value = mToken
      setToken(token.value)
      getBIMeUserInfo()
    }
    const mOrganizeId = route.query.organizeId
    if (!mOrganizeId) {
      organizeId.value = getOrganizeId()
      console.log('路由地址项目Id为空')
    } else {
      organizeId.value = mOrganizeId
      setOrganizeId(mOrganizeId)
    }
    getCCT()
  }
})
const ccToken = ref('')
/**
 * 获取流程token
 * @returns {Promise<void>}
 */
async function getCCT() {
  const res = await api.CCFlowLogin({
    Token: token.value,
    organizeId: organizeId.value
  })
  if (res.data.Ret === 1) {
    console.log('cc',res.data.Data)
    ccToken.value = res.data.Data
    getList()
    setCCToken(ccToken.value)
  }
}
/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  try {
    if (navigator.userAgent.indexOf('Android') > -1){
      mjs.clickBack()
    }else if (navigator.userAgent.indexOf('iPhone') > -1) {
      window.webkit.messageHandlers.clickBack.postMessage(null)
    }
  }catch(error){
    router.back()
  }
}
/**
 * 获取列表数据
 * @type {Ref<UnwrapRef<*[]>>}
 */
async function getList() {
  const result = await api.GetFlowList(flowType.value,{
    Token: ccToken.value,
    organizeId: organizeId.value
  })
  loading.value = false;
  finished.value = true;
  doneData.value = []
  // 处理数据 重组数据进行分类
  handleData( result.data,flowType.value)
}

/**
 * 重组数据进行分类
 */
const doneData = ref([])
function handleData(flowData,flowType) {
  let mapData= {}
  for (let i = 0; i < flowData.length; i++) {
    let ai = flowData[i]
    if (flowType === 'Todolist_Init'){
      if (!mapData[ai.NodeName]) {
        mapData[ai.NodeName] = [ai]
      } else {
        mapData[ai.NodeName].push(ai)
      }
    }else{
      if (!mapData[ai.FlowName]) {
        mapData[ai.FlowName] = [ai]
      } else {
        mapData[ai.FlowName].push(ai)
      }
    }
  }
  Object.keys(mapData).forEach(key => {
    doneData.value.push({
      name: key,
      type: flowType.value,
      data: mapData[key],
    })
  })
  console.log('分类后数据',doneData.value)
}

/**
 * 点击item
 * @param item
 */
function clickItem(item) {
  router.push({
    path:'/flowFolder',
    state:{
      data: JSON.stringify(item)
    }
  })
}
/**
 * 列表加载方法
 */
function onLoad(){

}
const refreshing = ref(false);

/**
 * 刷新列表
 */
function onRefresh() {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  // 加载数据
  getList();
  // 取消刷新状态
  refreshing.value = false
}
const projectOption = ref([])
const organizeId = ref('')

/**
 * 获取项目列表
 * @returns {Promise<void>}
 */
async function getProjectList() {
  const res = await api.GetProjectList({
    token: token.value,
    pageNum: 1,
    pageSize: 99999,
    keyword: ''
  })
  if (res.data.Ret === 1) {
    const projectList = res.data.Data.rows
    if (projectList.length > 0) {
      for (const projectListElement of projectList) {
        projectOption.value.push(
            {
              text: projectListElement.ProjectName,
              value: projectListElement.ProjectId
            }
        )
      }
      if (getOrganizeId()) {
        organizeId.value = getOrganizeId()
        // 不为空
      } else {
        organizeId.value = projectOption.value[0].value
        setOrganizeId(organizeId.value)
      }
      // await getList()
      // await getTypes()
    }
  }
}

/**
 * 获取外部token
 * @returns {Promise<void>}
 */
async function getOuterToken(outerToken) {
  const res = await api.LongyunLogin(outerToken)
  if (res.data.Ret === 1){
    token.value = res.data.Data.token
    setToken(token.value)
    await getProjectList()
    await getBIMeUserInfo()
  }else {
    showToast(res.data.Msg)
  }
  console.log('获取token',res)
}
/**
 * 获取用户信息并保存
 */
async function getBIMeUserInfo() {
  const res = await api.GetUser({
    token: token.value
  })
  if (res.data.Ret === 1){
    setUserInfo(JSON.stringify(res.data.Data))
  }
}

watch(organizeId, async (newValue) => {
  setOrganizeId(newValue)
  await getCCT()
})
</script>

<style scoped lang="scss">
.flow-container{
  height: 100%;
  background-color: #f5f7fb;
  .list-container{
    height: 100%;
    padding-top: 10px;
    background-color: #f5f7fb;
    .van-pull-refresh{
      height: 100%;
      .item-wrapper{
        border-radius: 8px;
        height: 50px;
        background-color: white;
        margin-left: 16px;
        margin-right: 16px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        position: relative;
        .item-img{
          margin-left: 20px;
          margin-right: 20px;
        }
        span{
          font-size: 14px;
          font-weight: 500;
        }
        .item-nums{
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 12px;
          top: 0;
          right: 0;
          position: absolute;
          width: 40px;
          height: 16px;
          background: #007AFE;
          border-radius: 0 8px 0 8px;
        }
      }
    }
  }

}

</style>